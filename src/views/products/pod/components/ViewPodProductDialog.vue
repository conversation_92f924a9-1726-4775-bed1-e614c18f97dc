<template>
  <el-dialog
    v-model="dialogVisible"
    width="1200px"
    :before-close="handleClose"
    :show-close="false"
    class="modern-dialog"
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-dark-text">POD商品详情</h3>
            <p class="text-sm text-gray-600 dark:text-dark-text-secondary">ID: {{ product?.id || '' }}</p>
          </div>
        </div>
        <button @click="handleClose"
          class="p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </template>

    <div v-if="product" class="p-6 space-y-8">
      <!-- 商品基本信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- 商品图片 -->
        <div>
          <div class="mb-4">
            <el-image
              :src="product.coverImage"
              :preview-src-list="[product.coverImage]"
              fit="cover"
              class="w-full h-80 object-cover rounded-lg border border-gray-200 dark:border-dark-border"
              :preview-teleported="true"
            />
          </div>
          
          <!-- 合成信息 -->
          <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
            <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">合成信息</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-purple-700 dark:text-purple-300">基础商品:</span>
                <span class="text-purple-900 dark:text-purple-100 font-medium">{{ product.baseProduct }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-purple-700 dark:text-purple-300">印刷图案:</span>
                <span class="text-purple-900 dark:text-purple-100 font-medium">自定义图案</span>
              </div>
              <div class="flex justify-between">
                <span class="text-purple-700 dark:text-purple-300">合成时间:</span>
                <span class="text-purple-900 dark:text-purple-100 font-medium">{{ product.createTime }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="space-y-6">
          <div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ product.name }}</h2>
            <div class="mt-2 flex items-center space-x-4">
              <div class="text-xl font-bold text-purple-600 dark:text-purple-400">
                ¥{{ product.minPrice.toFixed(2) }}
              </div>
              <div class="text-sm text-gray-500 dark:text-dark-text-secondary">
                SKU数量: {{ product.skuCount }}
              </div>
            </div>
          </div>

          <!-- 刊登状态 -->
          <div class="border-t border-gray-100 dark:border-dark-border pt-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-3">刊登状态</h3>
            <div class="flex items-center space-x-3">
              <span :class="getPublishStatusClass(product.publishStatus)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                {{ getPublishStatusText(product.publishStatus) }}
              </span>
              <div v-if="product.publishStatus === 'published'" class="text-sm text-gray-600 dark:text-dark-text-secondary">
                已刊登到 3 个平台
              </div>
            </div>
          </div>

          <div class="border-t border-gray-100 dark:border-dark-border pt-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-3">创建信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary">创建人</p>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ product.creator }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary">创建时间</p>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ product.createTime }}</p>
              </div>
            </div>
          </div>

          <div class="border-t border-gray-100 dark:border-dark-border pt-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-3">操作</h3>
            <div class="flex space-x-3">
              <el-button type="primary" @click="publishProduct">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                刊登商品
              </el-button>
              <el-button @click="downloadProduct">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                下载图片
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 合成详情 -->
      <div class="border-t border-gray-100 dark:border-dark-border pt-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">合成详情</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 基础商品 -->
          <div class="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-dark-text mb-3">基础商品</h4>
            <div class="flex items-center space-x-3">
              <img src="https://picsum.photos/100/100?random=201" alt="基础商品" class="w-16 h-16 rounded-lg object-cover" />
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ product.baseProduct }}</p>
                <p class="text-xs text-gray-500 dark:text-dark-text-secondary">白品商品</p>
              </div>
            </div>
          </div>

          <!-- 印刷图案 -->
          <div class="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-dark-text mb-3">印刷图案</h4>
            <div class="flex items-center space-x-3">
              <img src="https://picsum.photos/100/100?random=202" alt="印刷图案" class="w-16 h-16 rounded-lg object-cover" />
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">自定义图案</p>
                <p class="text-xs text-gray-500 dark:text-dark-text-secondary">来源: 图库</p>
              </div>
            </div>
          </div>

          <!-- 合成结果 -->
          <div class="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-dark-text mb-3">合成结果</h4>
            <div class="flex items-center space-x-3">
              <img :src="product.coverImage" alt="合成结果" class="w-16 h-16 rounded-lg object-cover" />
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">POD商品</p>
                <p class="text-xs text-gray-500 dark:text-dark-text-secondary">{{ product.skuCount }} 个SKU</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- SKU信息 -->
      <div class="border-t border-gray-100 dark:border-dark-border pt-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">SKU信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="(sku, index) in mockSkus" :key="index" class="bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border">
            <div class="flex items-center space-x-3 mb-3">
              <img :src="sku.image" alt="SKU图片" class="w-12 h-12 rounded-lg object-cover" />
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ sku.name }}</p>
                <p class="text-xs text-gray-500 dark:text-dark-text-secondary">{{ sku.id }}</p>
              </div>
            </div>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-dark-text-secondary">价格:</span>
                <span class="font-medium text-green-600 dark:text-green-400">¥{{ sku.price }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-dark-text-secondary">库存:</span>
                <span class="font-medium text-gray-900 dark:text-dark-text">{{ sku.stock }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50">
        <button @click="handleClose"
          class="px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200">
          关闭
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElMessage } from 'element-plus';

// Props
const props = defineProps<{
  modelValue: boolean;
  product: any;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 模拟SKU数据
const mockSkus = ref([
  {
    id: 'SKU001',
    name: 'S码 - 白色',
    price: 79.9,
    stock: 100,
    image: 'https://picsum.photos/100/100?random=301'
  },
  {
    id: 'SKU002',
    name: 'M码 - 白色',
    price: 79.9,
    stock: 80,
    image: 'https://picsum.photos/100/100?random=302'
  },
  {
    id: 'SKU003',
    name: 'L码 - 白色',
    price: 79.9,
    stock: 90,
    image: 'https://picsum.photos/100/100?random=303'
  },
  {
    id: 'SKU004',
    name: 'S码 - 黑色',
    price: 79.9,
    stock: 70,
    image: 'https://picsum.photos/100/100?random=304'
  },
  {
    id: 'SKU005',
    name: 'M码 - 黑色',
    price: 79.9,
    stock: 85,
    image: 'https://picsum.photos/100/100?random=305'
  },
  {
    id: 'SKU006',
    name: 'L码 - 黑色',
    price: 79.9,
    stock: 65,
    image: 'https://picsum.photos/100/100?random=306'
  }
]);

// 方法
const handleClose = () => {
  dialogVisible.value = false;
};

const getPublishStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'published': 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300',
    'publishing': 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300',
    'unpublished': 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300'
  };
  return statusClasses[status] || statusClasses['unpublished'];
};

const getPublishStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'published': '已刊登',
    'publishing': '刊登中',
    'unpublished': '未刊登'
  };
  return statusTexts[status] || '未知';
};

const publishProduct = () => {
  ElMessage.success('正在刊登POD商品...');
};

const downloadProduct = () => {
  ElMessage.success('正在下载商品图片...');
};
</script>

<style scoped>
.modern-dialog :deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.modern-dialog :deep(.el-dialog__footer) {
  padding: 0;
}
</style>
