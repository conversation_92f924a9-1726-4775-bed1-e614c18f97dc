<template>
  <el-dialog
    v-model="dialogVisible"
    width="1200px"
    :before-close="handleClose"
    :show-close="false"
    class="modern-dialog"
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-dark-text">POD合成任务详情</h3>
            <p class="text-sm text-gray-600 dark:text-dark-text-secondary">任务ID: {{ task?.id || '' }}</p>
          </div>
        </div>
        <button @click="handleClose"
          class="p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </template>

    <div v-if="task" class="p-6 space-y-8">
      <!-- 任务基本信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- 合成结果 -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">合成结果</h3>
          <div class="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
            <div class="flex items-center space-x-4 mb-4">
              <el-image
                :src="task.resultImage"
                :preview-src-list="[task.resultImage]"
                fit="cover"
                class="w-24 h-24 rounded-lg border border-gray-200 dark:border-dark-border"
                :preview-teleported="true"
              />
              <div>
                <h4 class="text-lg font-medium text-gray-900 dark:text-dark-text">{{ task.productName }}</h4>
                <p class="text-sm text-gray-600 dark:text-dark-text-secondary">基于: {{ task.baseProduct }}</p>
                <div class="flex items-center space-x-4 mt-2">
                  <span :class="getStatusClass(task.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ getStatusText(task.status) }}
                  </span>
                  <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                    成功: {{ task.successCount }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 任务信息 -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">任务信息</h3>
          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary">任务ID</p>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text font-mono">{{ task.id }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary">创建人</p>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ task.operator }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary">创建时间</p>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ task.createTime }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary">处理时长</p>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ getProcessingTime(task) }}</p>
              </div>
            </div>

            <!-- 合成统计 -->
            <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
              <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-3">合成统计</h4>
              <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ task.baseCount }}</p>
                  <p class="text-xs text-purple-700 dark:text-purple-300">白品数量</p>
                </div>
                <div>
                  <p class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ task.patternCount }}</p>
                  <p class="text-xs text-purple-700 dark:text-purple-300">图案数量</p>
                </div>
                <div>
                  <p class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ task.successCount }}</p>
                  <p class="text-xs text-purple-700 dark:text-purple-300">成功数量</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 合成详情 -->
      <div class="border-t border-gray-100 dark:border-dark-border pt-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">合成详情</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 基础商品 -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-dark-text mb-3">基础商品</h4>
            <div class="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
              <div class="flex items-center space-x-3">
                <img src="https://picsum.photos/100/100?random=701" alt="基础商品" class="w-16 h-16 rounded-lg object-cover" />
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ task.baseProduct }}</p>
                  <p class="text-xs text-gray-500 dark:text-dark-text-secondary">白品商品</p>
                  <p class="text-sm text-green-600 dark:text-green-400 font-medium mt-1">¥39.90</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 印刷图案 -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-dark-text mb-3">印刷图案 ({{ task.patternCount }})</h4>
            <div class="grid grid-cols-3 gap-2">
              <div v-for="i in Math.min(task.patternCount, 6)" :key="i" class="bg-gray-50 dark:bg-dark-card rounded-lg p-2">
                <img :src="`https://picsum.photos/100/100?random=${700 + i}`" :alt="`图案${i}`" class="w-full h-16 rounded-lg object-cover" />
                <p class="text-xs text-gray-600 dark:text-dark-text-secondary mt-1 text-center">图案{{ i }}</p>
              </div>
              <div v-if="task.patternCount > 6" class="bg-gray-100 dark:bg-gray-700 rounded-lg p-2 flex items-center justify-center">
                <span class="text-xs text-gray-500 dark:text-gray-400">+{{ task.patternCount - 6 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 生成的商品 -->
      <div class="border-t border-gray-100 dark:border-dark-border pt-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text">生成的POD商品</h3>
          <div class="flex space-x-2">
            <el-button size="small" @click="publishAllProducts">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              批量刊登
            </el-button>
            <el-button size="small" @click="downloadAllImages">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
              下载图片
            </el-button>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="i in task.successCount" :key="i" class="bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border">
            <div class="flex items-center space-x-3 mb-3">
              <img :src="`https://picsum.photos/100/100?random=${800 + i}`" :alt="`商品${i}`" class="w-16 h-16 rounded-lg object-cover" />
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ task.productName }} - 图案{{ i }}</p>
                <p class="text-xs text-gray-500 dark:text-dark-text-secondary">POD{{ String(i).padStart(3, '0') }}</p>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <div class="space-y-1">
                <div class="flex justify-between text-xs">
                  <span class="text-gray-600 dark:text-dark-text-secondary">价格:</span>
                  <span class="font-medium text-green-600 dark:text-green-400">¥79.90</span>
                </div>
                <div class="flex justify-between text-xs">
                  <span class="text-gray-600 dark:text-dark-text-secondary">SKU:</span>
                  <span class="font-medium text-gray-900 dark:text-dark-text">6个</span>
                </div>
              </div>
              <div class="flex space-x-1">
                <button @click="viewPodProduct(i)" class="p-1 text-purple-600 hover:text-purple-700 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>
                <button @click="publishPodProduct(i)" class="p-1 text-blue-600 hover:text-blue-700 transition-colors">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50">
        <div class="flex space-x-3">
          <el-button type="primary" @click="publishAllProducts">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            批量刊登
          </el-button>
          <el-button @click="downloadAllImages">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            下载图片
          </el-button>
        </div>
        <button @click="handleClose"
          class="px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200">
          关闭
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElMessage } from 'element-plus';

// Props
const props = defineProps<{
  modelValue: boolean;
  task: any;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const handleClose = () => {
  dialogVisible.value = false;
};

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'completed': 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300',
    'processing': 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300',
    'failed': 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300',
    'pending': 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300'
  };
  return statusClasses[status] || statusClasses['pending'];
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'completed': '已完成',
    'processing': '处理中',
    'failed': '失败',
    'pending': '等待中'
  };
  return statusTexts[status] || '未知';
};

const getProcessingTime = (task: any) => {
  // 模拟处理时长计算
  if (task.status === 'completed') {
    return '2分30秒';
  } else if (task.status === 'processing') {
    return '进行中...';
  } else if (task.status === 'failed') {
    return '失败';
  }
  return '等待中';
};

const viewPodProduct = (index: number) => {
  ElMessage.info(`查看POD商品 ${index}`);
};

const publishPodProduct = (index: number) => {
  ElMessage.success(`正在刊登POD商品 ${index}`);
};

const publishAllProducts = () => {
  ElMessage.success(`正在批量刊登 ${props.task?.successCount || 0} 个POD商品`);
};

const downloadAllImages = () => {
  ElMessage.success(`正在下载 ${props.task?.successCount || 0} 个商品图片`);
};
</script>

<style scoped>
.modern-dialog :deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.modern-dialog :deep(.el-dialog__footer) {
  padding: 0;
}
</style>
